import sqlite3
import pandas as pd

DB_FILE = "database.db"
CSV_FILE = "meals.csv"

def export_to_csv():
    try:
        # Connect to the SQLite database
        conn = sqlite3.connect(DB_FILE)
        
        # Read the meals table into a pandas DataFrame
        df = pd.read_sql_query("SELECT * FROM meals", conn)
        
        # Close the connection
        conn.close()
        
        # Write the DataFrame to a CSV file
        df.to_csv(CSV_FILE, index=False, encoding='utf-8-sig')
        
        print(f"Data successfully exported to {CSV_FILE}")
        
    except Exception as e:
        print(f"An error occurred: {e}")

if __name__ == "__main__":
    export_to_csv()
