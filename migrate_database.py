import sqlite3
import uuid
import os
from datetime import datetime

def migrate_database():
    """
    Migrate from old database structure (one record per day with breakfast, lunch, dinner columns)
    to new structure (one record per meal with date, meal_type, content columns)
    """
    
    old_db_path = "backend/database.db"
    new_db_path = "backend/database_new.db"
    backup_db_path = "backend/database_backup.db"
    
    # Create backup of original database
    if os.path.exists(old_db_path):
        print("Creating backup of original database...")
        import shutil
        shutil.copy2(old_db_path, backup_db_path)
        print(f"Backup created: {backup_db_path}")
    
    # Connect to old database
    old_conn = sqlite3.connect(old_db_path)
    old_cursor = old_conn.cursor()
    
    # Remove new database if it exists
    if os.path.exists(new_db_path):
        os.remove(new_db_path)

    # Create new database with new structure
    new_conn = sqlite3.connect(new_db_path)
    new_cursor = new_conn.cursor()
    
    # Create new table structure
    new_cursor.execute('''
        CREATE TABLE meals (
            id TEXT PRIMARY KEY,
            date DATE,
            meal_type TEXT,
            content TEXT DEFAULT ""
        )
    ''')
    
    # Create indexes
    new_cursor.execute('CREATE INDEX ix_meals_date ON meals (date)')
    new_cursor.execute('CREATE INDEX ix_meals_meal_type ON meals (meal_type)')
    new_cursor.execute('CREATE INDEX ix_meals_id ON meals (id)')
    
    try:
        # Get all records from old database
        old_cursor.execute('SELECT date, breakfast, lunch, dinner FROM meals')
        old_records = old_cursor.fetchall()
        
        print(f"Found {len(old_records)} records to migrate...")
        
        # Migrate each record
        for record in old_records:
            date_str, breakfast, lunch, dinner = record
            
            # Insert breakfast if not empty
            if breakfast and breakfast.strip():
                meal_id = str(uuid.uuid4())
                new_cursor.execute(
                    'INSERT INTO meals (id, date, meal_type, content) VALUES (?, ?, ?, ?)',
                    (meal_id, date_str, 'breakfast', breakfast)
                )
            
            # Insert lunch if not empty
            if lunch and lunch.strip():
                meal_id = str(uuid.uuid4())
                new_cursor.execute(
                    'INSERT INTO meals (id, date, meal_type, content) VALUES (?, ?, ?, ?)',
                    (meal_id, date_str, 'lunch', lunch)
                )
            
            # Insert dinner if not empty
            if dinner and dinner.strip():
                meal_id = str(uuid.uuid4())
                new_cursor.execute(
                    'INSERT INTO meals (id, date, meal_type, content) VALUES (?, ?, ?, ?)',
                    (meal_id, date_str, 'dinner', dinner)
                )
        
        # Commit changes
        new_conn.commit()
        
        # Verify migration
        new_cursor.execute('SELECT COUNT(*) FROM meals')
        new_count = new_cursor.fetchone()[0]
        print(f"Migration completed. New database has {new_count} meal records.")
        
        # Show sample of migrated data
        new_cursor.execute('SELECT date, meal_type, content FROM meals LIMIT 5')
        sample_records = new_cursor.fetchall()
        print("\nSample migrated records:")
        for record in sample_records:
            content_preview = record[2][:50] + "..." if len(record[2]) > 50 else record[2]
            print(f"  {record[0]} - {record[1]}: {content_preview}")
        
    except Exception as e:
        print(f"Error during migration: {e}")
        new_conn.rollback()
        return False
    
    finally:
        old_conn.close()
        new_conn.close()
    
    # Replace old database with new one
    if os.path.exists(old_db_path):
        os.remove(old_db_path)
    os.rename(new_db_path, old_db_path)
    
    print(f"\nMigration completed successfully!")
    print(f"Original database backed up to: {backup_db_path}")
    print(f"New database structure is now active.")
    
    return True

if __name__ == "__main__":
    migrate_database()
