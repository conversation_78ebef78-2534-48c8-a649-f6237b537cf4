from backend.main import SessionLocal, Meal

def debug_query():
    db = SessionLocal()
    
    # Get all meals
    all_meals = db.query(Meal).all()
    print(f"Total meals in database: {len(all_meals)}")
    
    for meal in all_meals:
        print(f"ID: {meal.id}, Date: '{meal.date}', Type: '{meal.meal_type}', Content: {meal.content[:50]}...")
    
    # Try specific query
    date_str = "2025-08-07"
    meals_for_date = db.query(Meal).filter(Meal.date == date_str).all()
    print(f"\nMeals for {date_str}: {len(meals_for_date)}")
    
    # Try different date formats
    for meal in all_meals:
        if "2025-08-07" in str(meal.date):
            print(f"Found meal with date containing 2025-08-07: '{meal.date}' (type: {type(meal.date)})")
    
    db.close()

if __name__ == "__main__":
    debug_query()
