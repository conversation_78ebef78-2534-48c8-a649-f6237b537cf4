
import os
from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Body, Response
from fastapi.middleware.cors import CORSMiddleware
from sqlalchemy import create_engine, Column, String, Text, Date
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from pydantic import BaseModel

import httpx
import json
import uuid
from datetime import date

# --- Configuration ---
DATABASE_URL = "sqlite:///./backend/database.db"
AI_API_URL = "http://192.168.50.105:3100/v1/chat/completions"
AI_API_KEY = "sk-VjVHdNK3DAXHHYtvmY1UFpC6pKfuNAJOypzqIme6faGad5Dx"

# --- Database Setup ---
engine = create_engine(DATABASE_URL, connect_args={"check_same_thread": False})
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
Base = declarative_base()

class Meal(Base):
    __tablename__ = "meals"
    id = Column(String, primary_key=True, index=True)  # UUID for each meal record
    date = Column(Date, index=True)  # Date field to match database schema
    meal_type = Column(String, index=True)  # 'breakfast', 'lunch', 'dinner'
    content = Column(Text, default="")

# Create the database tables if they don't exist
Base.metadata.create_all(bind=engine)

# --- Pydantic Models (for request/response validation) ---
class MealRecord(BaseModel):
    id: str
    date: str
    meal_type: str
    content: str

class MealData(BaseModel):
    date: str
    breakfast: str = ""
    lunch: str = ""
    dinner: str = ""

class SaveMealRequest(BaseModel):
    date: str
    meal_type: str
    content: str

class AIRequest(BaseModel):
    date: str
    requirements: str

# --- FastAPI App Initialization ---
app = FastAPI()

# CORS Middleware to allow frontend to communicate with backend
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://127.0.0.1:8080"],  # Allows all origins
    allow_credentials=True,
    allow_methods=["*"],  # Allows all methods
    allow_headers=["*"],  # Allows all headers
)

# --- API Endpoints ---

@app.get("/")
def read_root():
    return {"message": "Welcome to the Three Meals API"}

@app.get("/api/meals/{date_str}", response_model=MealData)
def get_meal_by_date(date_str: str):
    db = SessionLocal()

    # Convert date string to date object for querying
    meal_date = date.fromisoformat(date_str)
    meals = db.query(Meal).filter(Meal.date == meal_date).all()
    db.close()

    # Initialize result with empty values
    result = {"date": date_str, "breakfast": "", "lunch": "", "dinner": ""}

    # Fill in the meals that exist
    for meal in meals:
        if meal.meal_type == "breakfast":
            result["breakfast"] = meal.content
        elif meal.meal_type == "lunch":
            result["lunch"] = meal.content
        elif meal.meal_type == "dinner":
            result["dinner"] = meal.content

    return MealData(**result)

@app.post("/api/meals", response_model=MealRecord)
def save_meal(meal_data: SaveMealRequest, response: Response):
    response.headers["Access-Control-Allow-Origin"] = "http://127.0.0.1:8080"
    db = SessionLocal()

    # Convert date string to date object
    meal_date = date.fromisoformat(meal_data.date)

    # Check if a record already exists for this date and meal type
    existing_meal = db.query(Meal).filter(
        Meal.date == meal_date,
        Meal.meal_type == meal_data.meal_type
    ).first()

    if existing_meal:
        # Update existing record
        existing_meal.content = meal_data.content
        db.commit()
        db.refresh(existing_meal)
        result = MealRecord(
            id=existing_meal.id,
            date=existing_meal.date.isoformat(),
            meal_type=existing_meal.meal_type,
            content=existing_meal.content
        )
    else:
        # Create new record
        meal_id = str(uuid.uuid4())
        new_meal = Meal(
            id=meal_id,
            date=meal_date,
            meal_type=meal_data.meal_type,
            content=meal_data.content
        )
        db.add(new_meal)
        db.commit()
        db.refresh(new_meal)
        result = MealRecord(
            id=new_meal.id,
            date=new_meal.date.isoformat(),
            meal_type=new_meal.meal_type,
            content=new_meal.content
        )

    db.close()
    return result

@app.post("/api/generate-recipe")
async def generate_recipe(ai_request: AIRequest):
    prompt = f'''
        请为{ai_request.date}这一天，设计一份营养均衡、美味可口的三餐食谱。
        特殊饮食要求：{ai_request.requirements}。
        请严格按照以下JSON格式返回，不要有任何多余的解释或文字，只需返回纯JSON对象:
        {{
          "breakfast": "食谱内容和做法",
          "lunch": "食谱内容和做法",
          "dinner": "食谱内容和做法"
        }}
    '''
    async with httpx.AsyncClient() as client:
        try:
            response = await client.post(
                AI_API_URL,
                headers={
                    "Content-Type": "application/json",
                    "Authorization": f"Bearer {AI_API_KEY}"
                },
                json={
                    "model": "gemini-2.5-flash-preview-05-20",
                    "messages": [{"role": "user", "content": prompt}],
                    "temperature": 0.7,
                    "stream": False
                },
                timeout=30.0
            )
            response.raise_for_status() # Raise an exception for bad status codes (4xx or 5xx)
            
            data = response.json()
            content = data['choices'][0]['message']['content']
            
            # Extract the JSON part from the content string
            start_index = content.find('{')
            end_index = content.rfind('}') + 1
            
            if start_index != -1 and end_index != 0:
                json_string = content[start_index:end_index]
                try:
                    return json.loads(json_string)
                except json.JSONDecodeError:
                    raise HTTPException(status_code=500, detail="Failed to decode JSON from AI content.")
            else:
                raise HTTPException(status_code=500, detail="AI content did not contain a valid JSON object.")

        except httpx.RequestError as exc:
            raise HTTPException(status_code=500, detail=f"An error occurred while requesting the AI service: {exc}")
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"An unexpected error occurred: {e}")

if __name__ == "__main__":
    import uvicorn
    print("Starting backend server...")
    print("API documentation will be available at http://127.0.0.1:8001/docs")
    uvicorn.run(app, host="0.0.0.0", port=8001)
