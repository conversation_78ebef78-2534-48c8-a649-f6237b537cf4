:root {
    --bg-color: #f4f7f6;
    --card-bg-color: #ffffff;
    --text-color: #333;
    --primary-color: #5a8b88;
    --primary-light: #84c7c2;
    --accent-color: #f7b733;
    --shadow-color: rgba(0, 0, 0, 0.08);
    --border-radius: 20px;
}

body {
    font-family: 'Poppins', 'Helvetica Neue', 'Arial', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
    background-color: var(--bg-color);
    margin: 0;
    padding: 25px;
    color: var(--text-color);
    display: flex;
    justify-content: center;
    align-items: flex-start;
    min-height: 100vh;
}

.container {
    width: 100%;
    max-width: 1200px;
}

header {
    text-align: center;
    margin-bottom: 40px;
}

header h1 {
    font-size: 2.8rem;
    font-weight: 700;
    color: var(--primary-color);
    letter-spacing: 1px;
}

.ai-generator-card {
    background-color: var(--card-bg-color);
    border-radius: var(--border-radius);
    box-shadow: 0 10px 30px var(--shadow-color);
    padding: 30px;
    margin-bottom: 40px;
    display: grid;
    grid-template-columns: 1fr 1fr 200px;
    gap: 20px;
    align-items: center;
}

.input-group {
    display: flex;
    flex-direction: column;
}

.input-group label {
    margin-bottom: 8px;
    font-weight: 600;
    color: #555;
}

.input-group input {
    padding: 12px;
    border-radius: 10px;
    border: 1px solid #ddd;
    font-size: 1rem;
    font-family: inherit;
    transition: border-color 0.3s, box-shadow 0.3s;
}

.input-group input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(90, 139, 136, 0.2);
}

#generate-btn {
    grid-column: 3 / 4;
    align-self: end;
    border: none;
    padding: 14px 20px;
    border-radius: 10px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    background-image: linear-gradient(45deg, var(--accent-color), #fc4a1a);
    color: white;
    box-shadow: 0 5px 15px rgba(247, 183, 51, 0.4);
}

#generate-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(247, 183, 51, 0.5);
}

#generate-btn:disabled {
    background-image: none;
    background-color: #ccc;
    cursor: not-allowed;
    box-shadow: none;
    transform: none;
}

.meal-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 25px;
}

.meal-card {
    background-color: var(--card-bg-color);
    border-radius: var(--border-radius);
    box-shadow: 0 8px 25px var(--shadow-color);
    padding: 25px;
    display: flex;
    flex-direction: column;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.meal-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 30px rgba(0, 0, 0, 0.1);
}

.meal-card h2 {
    font-size: 1.8rem;
    margin-top: 0;
    margin-bottom: 20px;
    color: var(--text-color);
    border-bottom: 2px solid var(--primary-light);
    padding-bottom: 10px;
}

.meal-card textarea {
    width: 100%;
    height: 150px;
    border: 1px solid #ddd;
    border-radius: 10px;
    padding: 15px;
    font-size: 1rem;
    font-family: inherit;
    resize: vertical;
    margin-bottom: 20px;
    box-sizing: border-box;
    transition: border-color 0.3s ease;
}

.meal-card textarea:focus {
    outline: none;
    border-color: var(--primary-color);
}

.button-group {
    margin-top: auto;
    display: flex;
    gap: 10px;
}

.btn {
    flex-grow: 1;
    border: none;
    padding: 12px 15px;
    border-radius: 10px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
}

.btn-save {
    background-image: linear-gradient(45deg, var(--primary-light), var(--primary-color));
    color: white;
}

.btn-save:hover {
    opacity: 0.9;
    box-shadow: 0 5px 15px rgba(98, 165, 161, 0.4);
}

footer {
    text-align: center;
    margin-top: 50px;
    color: #aaa;
    font-size: 0.9rem;
}

@media (max-width: 768px) {
    .ai-generator-card {
        grid-template-columns: 1fr;
    }
    #generate-btn {
        grid-column: 1 / 2;
        width: 100%;
    }
}
