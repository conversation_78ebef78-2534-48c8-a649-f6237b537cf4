import sqlite3

def check_database():
    try:
        conn = sqlite3.connect('backend/database.db')
        cursor = conn.cursor()
        
        # Get table schema
        cursor.execute("PRAGMA table_info(meals)")
        schema = cursor.fetchall()
        print("Table schema:")
        for column in schema:
            print(f"  {column}")
        
        # Get all records
        cursor.execute('SELECT * FROM meals')
        rows = cursor.fetchall()
        print(f"\nDatabase contents ({len(rows)} records):")
        for i, row in enumerate(rows):
            # Truncate content for readability
            if len(row) >= 4:
                truncated_content = row[3][:100] + "..." if len(row[3]) > 100 else row[3]
                print(f"Record {i+1}: {row[0]}, {row[1]}, {row[2]}, {truncated_content}")
            else:
                print(f"Record {i+1}: {row}")
        
        conn.close()
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    check_database()
