<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI营养师 - 智能三餐规划</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="container">
        <header>
            <h1>🤖 AI营养师 - 智能三餐规划</h1>
        </header>

        <div class="ai-generator-card">
            <div class="input-group">
                <label for="date-picker">选择日期</label>
                <input type="date" id="date-picker">
            </div>
            <div class="input-group">
                <label for="requirements">个性化需求 (可选)</label>
                <input type="text" id="requirements" placeholder="例如: 低脂、素食、增肌...">
            </div>
            <button id="generate-btn">✨ AI一键生成</button>
        </div>

        <main class="meal-grid">
            <div class="meal-card" id="breakfast-card">
                <h2>早餐 🍳</h2>
                <textarea id="breakfast-output" placeholder="点击上方按钮，让AI为您推荐早餐食谱..."></textarea>
                <div class="button-group">
                    <button class="btn btn-save" data-meal="breakfast">保存记录</button>
                </div>
            </div>

            <div class="meal-card" id="lunch-card">
                <h2>午餐 🥗</h2>
                <textarea id="lunch-output" placeholder="点击上方按钮，让AI为您推荐午餐食谱..."></textarea>
                <div class="button-group">
                    <button class="btn btn-save" data-meal="lunch">保存记录</button>
                </div>
            </div>

            <div class="meal-card" id="dinner-card">
                <h2>晚餐 🍝</h2>
                <textarea id="dinner-output" placeholder="点击上方按钮，让AI为您推荐晚餐食谱..."></textarea>
                <div class="button-group">
                    <button class="btn btn-save" data-meal="dinner">保存记录</button>
                </div>
            </div>
        </main>

        <footer>
            <p>美好生活，从记录每一餐开始 ❤️</p>
        </footer>
    </div>

    <script src="script.js"></script>
</body>
</html>
