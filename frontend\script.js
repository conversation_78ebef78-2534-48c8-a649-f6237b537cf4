document.addEventListener('DOMContentLoaded', () => {
    const API_BASE_URL = 'http://127.0.0.1:8001';

    const datePicker = document.getElementById('date-picker');
    const requirementsInput = document.getElementById('requirements');
    const generateBtn = document.getElementById('generate-btn');
    
    const breakfastOutput = document.getElementById('breakfast-output');
    const lunchOutput = document.getElementById('lunch-output');
    const dinnerOutput = document.getElementById('dinner-output');

    // --- Initialize Page ---
    function initialize() {
        const today = new Date();
        const yyyy = today.getFullYear();
        const mm = String(today.getMonth() + 1).padStart(2, '0');
        const dd = String(today.getDate()).padStart(2, '0');
        const todayString = `${yyyy}-${mm}-${dd}`;
        
        datePicker.value = todayString;
        loadMealsForDate(todayString);

        generateBtn.addEventListener('click', handleGenerateClick);
        datePicker.addEventListener('change', () => loadMealsForDate(datePicker.value));
        
        document.querySelectorAll('.btn-save').forEach(button => {
            button.addEventListener('click', (event) => {
                const mealType = event.target.dataset.meal;
                handleSaveClick(mealType);
            });
        });
    }

    // --- Event Handlers ---
    async function handleGenerateClick() {
        const selectedDate = datePicker.value;
        if (!selectedDate) {
            alert('请先选择一个日期！');
            return;
        }

        const requirements = requirementsInput.value || '无特殊要求';

        generateBtn.disabled = true;
        generateBtn.textContent = '🧠 正在思考中...';
        setPlaceholders('AI正在为您准备建议...');

        try {
            const response = await fetch(`${API_BASE_URL}/api/generate-recipe`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ date: selectedDate, requirements: requirements })
            });

            if (!response.ok) {
                const errData = await response.json();
                throw new Error(errData.detail || 'AI生成失败');
            }

            const meals = await response.json();
            console.log('Meals received from backend:', meals);
            typeText(breakfastOutput, meals.breakfast || 'AI没有提供建议。');
            typeText(lunchOutput, meals.lunch || 'AI没有提供建议。');
            typeText(dinnerOutput, meals.dinner || 'AI没有提供建议。');

        } catch (error) {
            alert(`生成食谱时出错: ${error.message}`);
            console.error('AI食谱生成失败:', error);
            setPlaceholders('生成失败，请重试。');
        } finally {
            generateBtn.disabled = false;
            generateBtn.textContent = '✨ AI一键生成';
        }
    }

    async function handleSaveClick(mealType) {
        const date = datePicker.value;
        let mealValue;
        switch (mealType) {
            case 'breakfast':
                mealValue = breakfastOutput.value;
                break;
            case 'lunch':
                mealValue = lunchOutput.value;
                break;
            case 'dinner':
                mealValue = dinnerOutput.value;
                break;
        }

        const mealData = {
            date: date,
            meal_type: mealType,
            content: mealValue
        };

        try {
            const response = await fetch(`${API_BASE_URL}/api/meals`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(mealData)
            });

            if (!response.ok) {
                const errData = await response.json();
                throw new Error(errData.detail || '保存失败');
            }

            alert('记录已成功保存到数据库！');

        } catch (error) {
            alert(`保存记录时出错: ${error.message}`);
            console.error('保存失败:', error);
        }
    }

    async function loadMealsForDate(date) {
        if (!date) return;
        try {
            const response = await fetch(`${API_BASE_URL}/api/meals/${date}`);
            if (!response.ok) {
                // If 404 not found, it just means no record exists, which is fine.
                if (response.status === 404) {
                    clearOutputs();
                    return;
                }
                const errData = await response.json();
                throw new Error(errData.detail || '加载失败');
            }
            const meals = await response.json();
            breakfastOutput.value = meals.breakfast;
            lunchOutput.value = meals.lunch;
            dinnerOutput.value = meals.dinner;
        } catch (error) {
            alert(`加载记录时出错: ${error.message}`);
            console.error('加载失败:', error);
            clearOutputs();
        }
    }

    // --- Utility Functions ---
    function setPlaceholders(text) {
        breakfastOutput.placeholder = text;
        lunchOutput.placeholder = text;
        dinnerOutput.placeholder = text;
        breakfastOutput.value = "";
        lunchOutput.value = "";
        dinnerOutput.value = "";
    }
    
    function clearOutputs() {
        breakfastOutput.value = "";
        lunchOutput.value = "";
        dinnerOutput.value = "";
        setPlaceholders('今天吃什么？记录一下吧...');
    }

    function typeText(element, text, speed = 10) {
        let i = 0;
        element.value = '';
        function typing() {
            if (i < text.length) {
                element.value += text.charAt(i);
                i++;
                setTimeout(typing, speed);
            }
        }
        typing();
    }

    // --- Run on page load ---
    initialize();
});
