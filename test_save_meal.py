import requests
import json

def test_save_meal():
    url = "http://127.0.0.1:8001/api/meals"
    
    # Test saving a single breakfast meal
    meal_data = {
        "date": "2025-08-08",
        "meal_type": "breakfast",
        "content": "测试早餐：燕麦粥配水果"
    }
    
    print(f"Saving meal: {meal_data}")
    
    response = requests.post(url, json=meal_data)
    
    if response.status_code == 200:
        result = response.json()
        print(f"Success! Saved meal: {result}")
        
        # Now check if we can retrieve it
        get_url = f"http://127.0.0.1:8001/api/meals/2025-08-08"
        get_response = requests.get(get_url)
        
        if get_response.status_code == 200:
            retrieved_data = get_response.json()
            print(f"Retrieved data: {retrieved_data}")
            
            # Check if only breakfast is saved
            if (retrieved_data["breakfast"] == meal_data["content"] and 
                retrieved_data["lunch"] == "" and 
                retrieved_data["dinner"] == ""):
                print("✅ SUCCESS: Only breakfast was saved, lunch and dinner are empty!")
            else:
                print("❌ FAILED: Other meals were also affected")
        else:
            print(f"Failed to retrieve data: {get_response.status_code}")
    else:
        print(f"Failed to save meal: {response.status_code}, {response.text}")

if __name__ == "__main__":
    test_save_meal()
